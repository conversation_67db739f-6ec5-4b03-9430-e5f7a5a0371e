package com.seer.trick.fleet.service

import com.seer.trick.fleet.domain.MapPath
import com.seer.trick.fleet.domain.SceneContainerType
import com.seer.trick.fleet.domain.normalizeRadian
import com.seer.trick.fleet.traffic.venus.State
import org.slf4j.LoggerFactory
import kotlin.math.PI
import kotlin.math.abs

/**
 * 窄边通行服务
 * 负责处理机器人在窄边通行模式下的路径规划和容器旋转逻辑
 */
class NarrowSideAheadService(
  private val sr: SceneRuntime
) {
  private val logger = LoggerFactory.getLogger(javaClass)

  // 缓存容器类型信息，避免重复查询
  private val containerTypeCache = mutableMapOf<String, SceneContainerType>()

  // 角度容差，5度
  private val angleTolerance = Math.toRadians(5.0)

  /**
   * 检查是否需要进行窄边通行处理
   */
  fun checkNarrowSideAheadConditions(robotName: String, path: MapPath, fromState: State): Bo<PERSON>an {
    logger.debug("开始检查窄边通行条件 - 机器人: {}, 路径: {}->{}", robotName, path.fromPointName, path.toPointName)

    // 1. 检查路径是否设置了窄边通行
    if (!path.containerShortSideAhead) {
      logger.debug("窄边通行检查失败 - 路径未设置窄边通行: containerShortSideAhead={}", path.containerShortSideAhead)
      return false
    }
    logger.debug("✓ 路径已设置窄边通行: containerShortSideAhead={}", path.containerShortSideAhead)

    // 2. 检查机器人是否存在
    val rr = sr.robots[robotName]
    if (rr == null) {
      logger.debug("窄边通行检查失败 - 机器人不存在: {}", robotName)
      return false
    }
    logger.debug("✓ 机器人存在: {}", robotName)

    // 3. 检查机器人是否有自报信息
    val main = rr.selfReport?.main
    if (main == null) {
      logger.debug("窄边通行检查失败 - 机器人无自报信息: selfReport={}", rr.selfReport != null)
      return false
    }
    logger.debug("✓ 机器人有自报信息")

    // 4. 检查机器人是否有容器
    val loadRelations = main.loadRelations ?: emptyList()
    if (loadRelations.isEmpty()) {
      logger.debug("窄边通行检查失败 - 机器人无载货: loadRelations.size={}", loadRelations.size)
      return false
    }
    logger.debug("✓ 机器人有载货: loadRelations.size={}, 第一个载货类型={}, ID={}",
      loadRelations.size, loadRelations.first().type, loadRelations.first().id)

    // 5. 检查机器人组是否支持容器旋转（false为支持旋转）
    val group = rr.mustGetGroup()
    if (group.salverNotRotate) {
      logger.debug("窄边通行检查失败 - 机器人组不支持容器旋转: groupId={}, salverNotRotate={}",
        group.id, group.salverNotRotate)
      return false
    }
    logger.debug("✓ 机器人组支持容器旋转: groupId={}, salverNotRotate={}", group.id, group.salverNotRotate)

    // 6. 检查是否是超尺寸容器
    if (!group.containerOversize) {
      logger.debug("窄边通行检查失败 - 机器人组未配置超尺寸容器: groupId={}, containerOversize={}",
        group.id, group.containerOversize)
      return false
    }
    logger.debug("✓ 机器人组配置为超尺寸容器: groupId={}, containerOversize={}", group.id, group.containerOversize)

    // 7. 检查容器类型是否存在
    val containerType = getCachedContainerType(robotName)
    if (containerType == null) {
      logger.debug("窄边通行检查失败 - 无法获取容器类型信息")
      return false
    }
    logger.debug("✓ 获取到容器类型: name={}, outerLength={}, outerWidth={}",
      containerType.name, containerType.outerLength, containerType.outerWidth)

    // 8. 通过碰撞模型检查容器是否可独立旋转
    val collisionModel = RobotLoadCollisionService.buildCollisionModel(rr, main)
    if (!collisionModel.loaded || !collisionModel.loadRotatable) {
      logger.debug("窄边通行检查失败 - 碰撞模型检查失败: loaded={}, loadRotatable={}, containerTypeName={}",
        collisionModel.loaded, collisionModel.loadRotatable, collisionModel.containerTypeName)
      return false
    }
    logger.debug("✓ 碰撞模型检查通过: loaded={}, loadRotatable={}, containerTypeName={}, loadInitTheta={}",
      collisionModel.loaded, collisionModel.loadRotatable, collisionModel.containerTypeName, collisionModel.loadInitTheta)

    logger.info("窄边通行条件检查通过 - 机器人: {}, 路径: {}->{}, 容器类型: {}",
      robotName, path.fromPointName, path.toPointName, containerType.name)
    return true
  }

  /**
   * 调整货物朝向以适应窄边通行
   */
  fun calculateOptimalLoadThetas(
    robotName: String,
    originalLoadEnterTheta: Double,
    originalLoadExitTheta: Double,
    pathEnterTheta: Double,
    pathExitTheta: Double
  ): Pair<Double, Double> {
    // 获取容器类型信息
    val containerType = getCachedContainerType(robotName)
      ?: return originalLoadEnterTheta to originalLoadExitTheta
    val loadToRobotOffset = getLoadToRobotOffset(robotName)

    // 计算容器窄边偏移角度
    val narrowSideOffset = calculateContainerNarrowSideOffset(containerType)

    // 定义计算目标货物朝向的核心逻辑
    fun calculateOptimalLoadTheta(currentLoadTheta: Double, pathTheta: Double): Double {
      // 计算窄边通行所需的目标朝向：路径方向 + 窄边偏移 + 机器人货物偏移
      val targetTheta = (pathTheta + narrowSideOffset + loadToRobotOffset).normalizeRadian()

      // 计算当前朝向到目标朝向的角度差
      val angleDiff = calculateOptimalRotationAngle(currentLoadTheta, targetTheta)

      // 如果角度差很小（小于容差），认为已经满足窄边要求，不需要调整
      if (abs(angleDiff) < angleTolerance) {
        return currentLoadTheta
      }

      // 否则返回目标朝向
      return targetTheta
    }

    // 分别计算进入和退出时的最优货物朝向
    val adjustedLoadEnterTheta = calculateOptimalLoadTheta(originalLoadEnterTheta, pathEnterTheta)
    val adjustedLoadExitTheta = calculateOptimalLoadTheta(originalLoadExitTheta, pathExitTheta)

    return adjustedLoadEnterTheta to adjustedLoadExitTheta
  }

  /**
   * 计算容器的窄边方向偏移角度
   */
  private fun calculateContainerNarrowSideOffset(containerType: SceneContainerType): Double {
    return when {
      // 长宽相等：正方体，不区别窄边或者宽边，使用 0 度偏移
      abs(containerType.outerLength - containerType.outerWidth) < 0.01 -> 0.0
      // 长 > 宽：窄边方向为垂直于长度的方向，需要旋转 90° 使窄边朝前
      containerType.outerLength > containerType.outerWidth -> PI / 2
      // 宽 > 长：窄边就是长度方向，不需要旋转，0°
      else -> 0.0
    }
  }

  /**
   * 获取缓存的容器类型信息
   */
  private fun getCachedContainerType(robotName: String): SceneContainerType? {
    val rr = sr.robots[robotName] ?: return null
    val main = rr.selfReport?.main ?: return null

    // 检查机器人是否有载货
    val loadRelations = main.loadRelations ?: emptyList()
    if (loadRelations.isEmpty()) return null

    val group = rr.mustGetGroup()
    if (!group.containerOversize) return null

    // 获取容器信息
    val collisionModel = RobotLoadCollisionService.buildCollisionModel(rr, main)
    if (!collisionModel.loaded) return null

    val containerTypeName = collisionModel.containerTypeName ?: return null

    return containerTypeCache.getOrPut(containerTypeName) {
      sr.containerTypes.values.find { it.name == containerTypeName } ?: return null
    }
  }

  /**
   * 计算从当前朝向到目标朝向的最小角度差
   */
  private fun calculateOptimalRotationAngle(currentTheta: Double, targetTheta: Double): Double {
    var angleDiff = (targetTheta - currentTheta).normalizeRadian()

    // 如果角度差超过半圆（π），选择反向旋转
    if (abs(angleDiff) > PI) {
      angleDiff = if (angleDiff > 0) {
        angleDiff - 2 * PI
      } else {
        angleDiff + 2 * PI
      }
    }

    return angleDiff
  }

  /**
   * 获取机器人与货物之间的固定偏移角度
   */
  fun getLoadToRobotOffset(robotName: String): Double {
    val rr = sr.robots[robotName] ?: return 0.0
    val main = rr.selfReport?.main ?: return 0.0

    val collisionModel = RobotLoadCollisionService.buildCollisionModel(rr, main)
    return if (collisionModel.loaded) {
      // 使用配置中的货物初始角度作为偏移角
      collisionModel.loadInitTheta
    } else {
      0.0
    }
  }
}